import React from 'react';
import styles from './TopicTree.module.css';

function TopicTree({ curriculum, onTopicSelect, selectedTopic, subjectTitle }) {
  const handleTopicClick = (topic) => {
    onTopicSelect(topic);
  };

  if (!curriculum) {
    return (
      <div className={styles.topicTree}>
        <h2 className={styles.heading}>Loading...</h2>
      </div>
    );
  }

  return (
    <div className={styles.topicTree}>
      <h2 className={styles.heading}>{subjectTitle || 'Curriculum'}</h2>
      <ul className={styles.categoryList}>
        {curriculum.map((category) => (
          <li key={category.id} className={styles.category}>
            <h3 className={styles.categoryTitle}>{category.title}</h3>
            <ul className={styles.topicList}>
              {category.topics.map((topic) => (
                <li 
                  key={topic.id} 
                  className={`${styles.topic} ${selectedTopic?.id === topic.id ? styles.selected : ''}`}
                  onClick={() => handleTopicClick(topic)}
                >
                  {topic.title}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default TopicTree;
