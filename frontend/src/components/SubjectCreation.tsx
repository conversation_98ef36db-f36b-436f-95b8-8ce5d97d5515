import React, { useState } from 'react';
import axios from 'axios';
import CurriculumEditor from './CurriculumEditor.js';
import { useLanguage } from '../contexts/LanguageContext.js';
import styles from './SubjectCreation.module.css';

function SubjectCreation({ onBack, onSubjectCreated, apiKey }) {
  const { selectedLanguage } = useLanguage();
  const [step, setStep] = useState(1); // 1: topic input, 2: curriculum review, 3: curriculum editing
  const [subjectTitle, setSubjectTitle] = useState('');
  const [subjectDescription, setSubjectDescription] = useState('');
  const [generatedCurriculum, setGeneratedCurriculum] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);

  const handleGenerateCurriculum = async () => {
    if (!subjectTitle.trim()) {
      setError('Please enter a subject title');
      return;
    }

    if (!apiKey) {
      setError('Please set your API key in the settings');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await axios.post('http://localhost:3001/api/generate-curriculum', {
        subject: subjectTitle.trim(),
        language: selectedLanguage.name
      });

      setGeneratedCurriculum(response.data);
      setStep(2);
    } catch (error) {
      console.error('Error generating curriculum:', error);
      if (error.response) {
        setError(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        setError('Error: No response from server. Make sure the backend is running.');
      } else {
        setError(`Error: ${error.message}`);
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateSubject = async () => {
    try {
      // Create the subject
      const subjectResponse = await axios.post('http://localhost:3001/api/subjects', {
        title: subjectTitle.trim(),
        description: subjectDescription.trim()
      });

      const subjectId = subjectResponse.data.id;

      // Save the curriculum
      await axios.post('http://localhost:3001/api/curricula', {
        subjectId: subjectId,
        structure: generatedCurriculum
      });

      onSubjectCreated({
        id: subjectId,
        title: subjectTitle.trim(),
        description: subjectDescription.trim(),
        has_curriculum: true
      });
    } catch (error) {
      console.error('Error creating subject:', error);
      setError('Failed to create subject. Please try again.');
    }
  };

  const handleCurriculumEdit = (updatedCurriculum) => {
    setGeneratedCurriculum(updatedCurriculum);
  };

  if (step === 1) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button onClick={onBack} className={styles.backButton}>
            ← Back to Dashboard
          </button>
          <h1>Create New Learning Subject</h1>
        </div>

        <div className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="subjectTitle" className={styles.label}>
              What do you want to learn?
            </label>
            <input
              id="subjectTitle"
              type="text"
              value={subjectTitle}
              onChange={(e) => setSubjectTitle(e.target.value)}
              className={styles.input}
              placeholder="e.g., React.js, Machine Learning, Photography..."
              maxLength={100}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="subjectDescription" className={styles.label}>
              Description (optional)
            </label>
            <textarea
              id="subjectDescription"
              value={subjectDescription}
              onChange={(e) => setSubjectDescription(e.target.value)}
              className={styles.textarea}
              placeholder="Add any specific details about what you want to focus on..."
              maxLength={500}
              rows={3}
            />
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          <button
            onClick={handleGenerateCurriculum}
            disabled={isGenerating || !subjectTitle.trim()}
            className={styles.generateButton}
          >
            {isGenerating ? 'Generating Curriculum...' : 'Generate Curriculum'}
          </button>
        </div>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button onClick={() => setStep(1)} className={styles.backButton}>
            ← Back to Subject Details
          </button>
          <h1>Review Your Curriculum</h1>
        </div>

        <div className={styles.curriculumPreview}>
          <div className={styles.subjectInfo}>
            <h2>{subjectTitle}</h2>
            {subjectDescription && <p>{subjectDescription}</p>}
          </div>

          {generatedCurriculum && (
            <div className={styles.curriculum}>
              {generatedCurriculum.curriculum.map((category, categoryIndex) => (
                <div key={category.id} className={styles.category}>
                  <h3 className={styles.categoryTitle}>{category.title}</h3>
                  {category.description && (
                    <p className={styles.categoryDescription}>{category.description}</p>
                  )}
                  <ul className={styles.topicList}>
                    {category.topics.map((topic, topicIndex) => (
                      <li key={topic.id} className={styles.topic}>
                        <span className={styles.topicTitle}>{topic.title}</span>
                        {topic.description && (
                          <span className={styles.topicDescription}>{topic.description}</span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          <div className={styles.actions}>
            <button
              onClick={() => setStep(1)}
              className={styles.editButton}
            >
              Regenerate Curriculum
            </button>
            <button
              onClick={() => setStep(3)}
              className={styles.editButton}
            >
              Edit Curriculum
            </button>
            <button
              onClick={handleCreateSubject}
              className={styles.createButton}
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <button onClick={() => setStep(2)} className={styles.backButton}>
            ← Back to Review
          </button>
          <h1>Edit Curriculum</h1>
        </div>

        <div className={styles.editorContainer}>
          <div className={styles.subjectInfo}>
            <h2>{subjectTitle}</h2>
            {subjectDescription && <p>{subjectDescription}</p>}
          </div>

          <CurriculumEditor
            curriculum={generatedCurriculum}
            onCurriculumChange={handleCurriculumEdit}
          />

          <div className={styles.actions}>
            <button
              onClick={() => setStep(2)}
              className={styles.editButton}
            >
              Back to Review
            </button>
            <button
              onClick={handleCreateSubject}
              className={styles.createButton}
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

export default SubjectCreation;
