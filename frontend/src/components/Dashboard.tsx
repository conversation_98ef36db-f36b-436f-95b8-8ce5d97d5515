import React, { useState, useEffect } from 'react';
import axios from 'axios';
import styles from './Dashboard.module.css';

function Dashboard({ onSubjectSelect, onCreateSubject, onCreateUniversityCourse }) {
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('http://localhost:3001/api/subjects');
      setSubjects(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching subjects:', error);
      setError('Failed to load subjects. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSubject = async (subjectId, subjectTitle) => {
    if (!window.confirm(`Are you sure you want to delete "${subjectTitle}"? This will also delete all associated curricula and courses.`)) {
      return;
    }

    try {
      await axios.delete(`http://localhost:3001/api/subjects/${subjectId}`);
      setSubjects(subjects.filter(subject => subject.id !== subjectId));
    } catch (error) {
      console.error('Error deleting subject:', error);
      alert('Failed to delete subject. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p className={styles.loadingText}>Loading your subjects...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchSubjects} className={styles.retryButton}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Your Learning Subjects</h1>
        <div className={styles.headerButtons}>
          <button
            className={styles.createButton}
            onClick={onCreateSubject}
          >
            + Create New Subject
          </button>
          {onCreateUniversityCourse && (
            <button
              className={styles.universityButton}
              onClick={onCreateUniversityCourse}
            >
              🎓 Create University Course
            </button>
          )}
        </div>
      </div>

      {subjects.length === 0 ? (
        <div className={styles.emptyState}>
          <h2>No subjects yet</h2>
          <p>Create your first learning subject to get started!</p>
          <button 
            className={styles.createButtonLarge}
            onClick={onCreateSubject}
          >
            Create Your First Subject
          </button>
        </div>
      ) : (
        <div className={styles.subjectsGrid}>
          {subjects.map((subject) => (
            <div key={subject.id} className={styles.subjectCard}>
              <div className={styles.subjectHeader}>
                <h3 className={styles.subjectTitle}>{subject.title}</h3>
                <div className={styles.subjectActions}>
                  <button
                    className={styles.deleteButton}
                    onClick={() => handleDeleteSubject(subject.id, subject.title)}
                    title="Delete subject"
                  >
                    ×
                  </button>
                </div>
              </div>
              
              {subject.description && (
                <p className={styles.subjectDescription}>{subject.description}</p>
              )}
              
              <div className={styles.subjectMeta}>
                <span className={styles.curriculumStatus}>
                  {subject.has_curriculum ? '✓ Curriculum ready' : '⚠ No curriculum'}
                </span>
                <span className={styles.createdDate}>
                  Created {new Date(subject.created_at).toLocaleDateString()}
                </span>
              </div>
              
              <button
                className={styles.selectButton}
                onClick={() => onSubjectSelect(subject)}
                disabled={!subject.has_curriculum}
              >
                {subject.has_curriculum ? 'Start Learning' : 'Setup Required'}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default Dashboard;
