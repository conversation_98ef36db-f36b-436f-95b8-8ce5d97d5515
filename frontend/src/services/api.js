// API Configuration and Service
const API_BASE_URL = 'http://localhost:3001/api/v1';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('authToken');
  }

  // Set authentication token
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  // Get authentication token
  getToken() {
    return this.token || localStorage.getItem('authToken');
  }

  // Clear authentication
  clearAuth() {
    this.token = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  }

  // Make HTTP request with authentication
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Include cookies for session management
      ...options,
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);

      // Handle different response types
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = { message: await response.text() };
      }

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);

      // Handle authentication errors
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        this.clearAuth();
        window.location.href = '/login';
      }

      throw error;
    }
  }

  // GET request
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  // POST request
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // Authentication methods
  async register(userData) {
    const response = await this.post('/auth/register', userData);
    if (response.success && response.data.token) {
      this.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response;
  }

  async login(credentials) {
    const response = await this.post('/auth/login', credentials);
    if (response.success && response.data.token) {
      this.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response;
  }

  async logout() {
    try {
      await this.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuth();
    }
  }

  async getProfile() {
    return this.get('/users/profile');
  }

  async updateProfile(profileData) {
    return this.put('/users/profile', profileData);
  }

  // Subject methods
  async getSubjects(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/subjects${queryString ? `?${queryString}` : ''}`);
  }

  async getSubject(id) {
    return this.get(`/subjects/${id}`);
  }

  async createSubject(subjectData) {
    return this.post('/subjects', subjectData);
  }

  async updateSubject(id, subjectData) {
    return this.put(`/subjects/${id}`, subjectData);
  }

  async deleteSubject(id) {
    return this.delete(`/subjects/${id}`);
  }

  async generateCurriculum(subjectId, data) {
    return this.post(`/subjects/${subjectId}/generate-curriculum`, data);
  }

  async updateSubjectProgress(subjectId, progressData) {
    return this.post(`/subjects/${subjectId}/progress`, progressData);
  }

  async getSubjectProgress(subjectId) {
    return this.get(`/subjects/${subjectId}/progress`);
  }

  async getSubjectStats(subjectId) {
    return this.get(`/subjects/${subjectId}/stats`);
  }

  // University Course methods
  async generateUniversityCourse(data) {
    return this.post('/university-courses/generate', data);
  }

  async createUniversityCourse(courseData) {
    return this.post('/university-courses', courseData);
  }

  async getUniversityCourses(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/university-courses${queryString ? `?${queryString}` : ''}`);
  }

  async getUniversityCourse(id) {
    return this.get(`/university-courses/${id}`);
  }

  async updateUniversityCourse(id, courseData) {
    return this.put(`/university-courses/${id}`, courseData);
  }

  async deleteUniversityCourse(id) {
    return this.delete(`/university-courses/${id}`);
  }

  async updateUniversityCourseProgress(courseId, moduleId, progressData) {
    return this.post(`/university-courses/${courseId}/progress/${moduleId}`, progressData);
  }

  async getUniversityCourseStats(courseId) {
    return this.get(`/university-courses/${courseId}/stats`);
  }

  // Utility methods
  isAuthenticated() {
    return !!this.getToken();
  }

  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
