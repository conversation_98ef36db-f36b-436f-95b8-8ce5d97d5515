import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Supported languages with their display names and codes
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'de', name: 'German', nativeName: 'Deutsch' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  { code: 'sv', name: 'Swedish', nativeName: 'Svenska' },
  { code: 'no', name: 'Norwegian', nativeName: 'Norsk' },
  { code: 'da', name: 'Danish', nativeName: 'Dansk' },
  { code: 'fi', name: 'Finnish', nativeName: 'Suomi' },
  { code: 'pl', name: 'Polish', nativeName: 'Polski' },
  { code: 'tr', name: 'Turkish', nativeName: 'Türkçe' },
  { code: 'he', name: 'Hebrew', nativeName: 'עברית' }
];

const LANGUAGE_STORAGE_KEY = 'qtmaster-language-preference';

export const LanguageProvider = ({ children }) => {
  const [selectedLanguage, setSelectedLanguage] = useState(() => {
    // Try to get language from localStorage first
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (savedLanguage) {
      const parsedLanguage = JSON.parse(savedLanguage);
      // Verify the saved language is still supported
      const isSupported = SUPPORTED_LANGUAGES.find(lang => lang.code === parsedLanguage.code);
      if (isSupported) {
        return parsedLanguage;
      }
    }
    
    // Default to English if no saved preference or unsupported language
    return SUPPORTED_LANGUAGES[0]; // English
  });

  // Save language preference to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, JSON.stringify(selectedLanguage));
  }, [selectedLanguage]);

  const changeLanguage = (languageCode) => {
    const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
    if (language) {
      setSelectedLanguage(language);
    } else {
      console.warn(`Language code "${languageCode}" is not supported`);
    }
  };

  const getLanguageByCode = (code) => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === code);
  };

  const value = {
    selectedLanguage,
    changeLanguage,
    supportedLanguages: SUPPORTED_LANGUAGES,
    getLanguageByCode,
    isLanguageSupported: (code) => SUPPORTED_LANGUAGES.some(lang => lang.code === code)
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
