export const curriculum = [
  {
    id: "getting-started",
    title: "Getting Started with Qt",
    topics: [
      { id: "intro-to-qt", title: "Introduction to Qt" },
      { id: "installing-qt", title: "Installing Qt and PySide6" },
      { id: "first-qt-app", title: "Your First Qt Application" }
    ]
  },
  {
    id: "basic-widgets",
    title: "Basic Widgets",
    topics: [
      { id: "qpushbutton", title: "QPushButton" },
      { id: "qlabel", title: "QLabel" },
      { id: "qlineedit", title: "QLineEdit" },
      { id: "qtextedit", title: "QTextEdit" },
      { id: "layout-management", title: "Layout Management" }
    ]
  },
  {
    id: "signals-slots",
    title: "Signals and Slots",
    topics: [
      { id: "signals-slots-intro", title: "Introduction to Signals and Slots" },
      { id: "custom-signals", title: "Creating Custom Signals" },
      { id: "event-handling", title: "Event Handling" }
    ]
  },
  {
    id: "advanced-widgets",
    title: "Advanced Widgets",
    topics: [
      { id: "qtablewidget", title: "QTableWidget" },
      { id: "qlistwidget", title: "QListWidget" },
      { id: "qtreewidget", title: "QTreeWidget" },
      { id: "qmenubar", title: "QMenuBar and QMenu" },
      { id: "qtoolbar", title: "QToolBar" }
    ]
  },
  {
    id: "dialogs",
    title: "Dialogs",
    topics: [
      { id: "qdialog", title: "QDialog Basics" },
      { id: "qinputdialog", title: "QInputDialog" },
      { id: "qfiledialog", title: "QFileDialog" },
      { id: "qmessagebox", title: "QMessageBox" },
      { id: "custom-dialogs", title: "Creating Custom Dialogs" }
    ]
  },
  {
    id: "model-view",
    title: "Model/View Programming",
    topics: [
      { id: "model-view-intro", title: "Introduction to Model/View" },
      { id: "qabstractitemmodel", title: "QAbstractItemModel" },
      { id: "qstandarditemmodel", title: "QStandardItemModel" },
      { id: "custom-models", title: "Creating Custom Models" },
      { id: "delegates", title: "Using Delegates" }
    ]
  },
  {
    id: "graphics-view",
    title: "Graphics View Framework",
    topics: [
      { id: "graphics-view-intro", title: "Introduction to Graphics View" },
      { id: "qgraphicsscene", title: "QGraphicsScene" },
      { id: "qgraphicsview", title: "QGraphicsView" },
      { id: "qgraphicsitem", title: "QGraphicsItem" },
      { id: "custom-items", title: "Creating Custom Graphics Items" }
    ]
  },
  {
    id: "styling",
    title: "Styling Qt Applications",
    topics: [
      { id: "stylesheets", title: "Qt Stylesheets" },
      { id: "custom-styles", title: "Creating Custom Styles" },
      { id: "theming", title: "Application Theming" }
    ]
  },
  {
    id: "advanced-concepts",
    title: "Advanced Qt Concepts",
    topics: [
      { id: "multithreading", title: "Multithreading with QThread" },
      { id: "networking", title: "Networking with Qt" },
      { id: "databases", title: "Database Integration with QSql" },
      { id: "internationalization", title: "Internationalization (i18n)" },
      { id: "deploying", title: "Deploying Qt Applications" }
    ]
  }
];
